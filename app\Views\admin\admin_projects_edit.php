<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects') ?>" class="btn btn-secondary">
    ← Back to Projects
</a>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
    👁️ View Details
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Project
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update project information for: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header">
        Project Information
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <form action="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>" method="post" enctype="multipart/form-data" class="project-edit-form">

            <?= csrf_field() ?>
            
            <!-- Basic Information Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); border-bottom: 2px solid var(--bg-tertiary); padding-bottom: var(--spacing-sm);">
                    📋 Basic Information
                </h3>
                
                <!-- Project Code (Read-only) -->
                <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                    <label for="pro_code_display" class="form-label">Project Code</label>
                    <input
                        type="text"
                        id="pro_code_display"
                        class="form-input"
                        value="<?= esc($project['pro_code']) ?>"
                        readonly
                        style="background: var(--bg-tertiary); color: var(--text-muted);"
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Project code is automatically generated and cannot be changed
                    </small>
                </div>

                <!-- Project Status -->
                <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                    <label for="status" class="form-label">Project Status *</label>
                    <select id="status" name="status" class="form-input" required>
                        <option value="">Select Status</option>
                        <option value="planning" <?= (old('status', $project['status']) === 'planning') ? 'selected' : '' ?>>Planning</option>
                        <option value="active" <?= (old('status', $project['status']) === 'active') ? 'selected' : '' ?>>Active</option>
                        <option value="on-hold" <?= (old('status', $project['status']) === 'on-hold') ? 'selected' : '' ?>>On Hold</option>
                        <option value="completed" <?= (old('status', $project['status']) === 'completed') ? 'selected' : '' ?>>Completed</option>
                        <option value="cancelled" <?= (old('status', $project['status']) === 'cancelled') ? 'selected' : '' ?>>Cancelled</option>
                    </select>
                </div>

                <!-- Project Title -->
                <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                    <label for="title" class="form-label">Project Title *</label>
                    <input
                        type="text"
                        id="title"
                        name="title"
                        class="form-input"
                        value="<?= old('title', $project['title']) ?>"
                        placeholder="Enter project title"
                        required
                        autofocus
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Maximum 200 characters
                    </small>
                </div>

                <!-- Other Project IDs -->
                <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                    <label for="other_project_ids" class="form-label">Other Project IDs</label>
                    <input 
                        type="text" 
                        id="other_project_ids" 
                        name="other_project_ids" 
                        class="form-input" 
                        value="<?= old('other_project_ids', $project['other_project_ids']) ?>"
                        placeholder="External system references (comma-separated)"
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Cross-references to external systems (optional)
                    </small>
                </div>

                <!-- Project Goal -->
                <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                    <label for="goal" class="form-label">Project Goal</label>
                    <textarea 
                        id="goal" 
                        name="goal" 
                        class="form-input" 
                        rows="3"
                        placeholder="Describe the main goal of this project"
                    ><?= old('goal', $project['goal']) ?></textarea>
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Brief description of project objectives (max 1000 characters)
                    </small>
                </div>

                <!-- Project Description -->
                <div class="form-group">
                    <label for="description" class="form-label">Project Description</label>
                    <textarea 
                        id="description" 
                        name="description" 
                        class="form-input" 
                        rows="5"
                        placeholder="Detailed description of the project"
                    ><?= old('description', $project['description']) ?></textarea>
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Comprehensive project description (optional)
                    </small>
                </div>
            </div>

            <!-- Timeline Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); border-bottom: 2px solid var(--bg-tertiary); padding-bottom: var(--spacing-sm);">
                    📅 Timeline & Planning
                </h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                    
                    <!-- Initiation Date -->
                    <div class="form-group">
                        <label for="initiation_date" class="form-label">Initiation Date *</label>
                        <input
                            type="date"
                            id="initiation_date"
                            name="initiation_date"
                            class="form-input"
                            value="<?= old('initiation_date', $project['initiation_date']) ?>"
                            required
                        >
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Required field - used for project code generation
                        </small>
                    </div>

                    <!-- Start Date -->
                    <div class="form-group">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input 
                            type="date" 
                            id="start_date" 
                            name="start_date" 
                            class="form-input" 
                            value="<?= old('start_date', $project['start_date']) ?>"
                        >
                    </div>

                    <!-- End Date -->
                    <div class="form-group">
                        <label for="end_date" class="form-label">End Date</label>
                        <input 
                            type="date" 
                            id="end_date" 
                            name="end_date" 
                            class="form-input" 
                            value="<?= old('end_date', $project['end_date']) ?>"
                        >
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg);">
                    
                    <!-- Baseline Year -->
                    <div class="form-group">
                        <label for="baseline_year" class="form-label">Baseline Year</label>
                        <input 
                            type="number" 
                            id="baseline_year" 
                            name="baseline_year" 
                            class="form-input" 
                            value="<?= old('baseline_year', $project['baseline_year']) ?>"
                            min="1900" 
                            max="2100"
                            placeholder="e.g., 2024"
                        >
                    </div>

                    <!-- Target Year -->
                    <div class="form-group">
                        <label for="target_year" class="form-label">Target Year</label>
                        <input 
                            type="number" 
                            id="target_year" 
                            name="target_year" 
                            class="form-input" 
                            value="<?= old('target_year', $project['target_year']) ?>"
                            min="1900" 
                            max="2100"
                            placeholder="e.g., 2025"
                        >
                    </div>
                </div>
            </div>

            <!-- Location Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); border-bottom: 2px solid var(--bg-tertiary); padding-bottom: var(--spacing-sm);">
                    📍 Location Information
                </h3>
                
                <!-- Address Line -->
                <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                    <label for="address_line" class="form-label">Address Line</label>
                    <input 
                        type="text" 
                        id="address_line" 
                        name="address_line" 
                        class="form-input" 
                        value="<?= old('address_line', $project['address_line']) ?>"
                        placeholder="Street address or location description"
                    >
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                    
                    <!-- Country -->
                    <div class="form-group">
                        <label for="country_id" class="form-label">Country</label>
                        <select id="country_id" name="country_id" class="form-input">
                            <option value="">Select Country</option>
                            <?php foreach ($countries as $country): ?>
                                <option value="<?= $country['id'] ?>" <?= (old('country_id', $project['country_id']) == $country['id']) ? 'selected' : '' ?>>
                                    <?= esc($country['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Province -->
                    <div class="form-group">
                        <label for="province_id" class="form-label">Province</label>
                        <select id="province_id" name="province_id" class="form-input">
                            <option value="">Select Province</option>
                            <?php foreach ($provinces as $province): ?>
                                <option value="<?= $province['id'] ?>"
                                        data-country-id="<?= $province['country_id'] ?>"
                                        <?= (old('province_id', $project['province_id']) == $province['id']) ? 'selected' : '' ?>>
                                    <?= esc($province['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">

                    <!-- District -->
                    <div class="form-group">
                        <label for="district_id" class="form-label">District</label>
                        <select id="district_id" name="district_id" class="form-input">
                            <option value="">Select District</option>
                            <?php foreach ($districts as $district): ?>
                                <option value="<?= $district['id'] ?>"
                                        data-province-id="<?= $district['province_id'] ?>"
                                        <?= (old('district_id', $project['district_id']) == $district['id']) ? 'selected' : '' ?>>
                                    <?= esc($district['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- LLG -->
                    <div class="form-group">
                        <label for="llg_id" class="form-label">Local Level Government</label>
                        <select id="llg_id" name="llg_id" class="form-input">
                            <option value="">Select LLG</option>
                            <?php foreach ($llgs as $llg): ?>
                                <option value="<?= $llg['id'] ?>"
                                        data-district-id="<?= $llg['district_id'] ?>"
                                        <?= (old('llg_id', $project['llg_id']) == $llg['id']) ? 'selected' : '' ?>>
                                    <?= esc($llg['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: var(--spacing-lg);">
                    
                    <!-- Ward Name -->
                    <div class="form-group">
                        <label for="ward_name" class="form-label">Ward Name</label>
                        <input 
                            type="text" 
                            id="ward_name" 
                            name="ward_name" 
                            class="form-input" 
                            value="<?= old('ward_name', $project['ward_name']) ?>"
                            placeholder="Ward name"
                        >
                    </div>

                    <!-- Village Name -->
                    <div class="form-group">
                        <label for="village_name" class="form-label">Village Name</label>
                        <input 
                            type="text" 
                            id="village_name" 
                            name="village_name" 
                            class="form-input" 
                            value="<?= old('village_name', $project['village_name']) ?>"
                            placeholder="Village name"
                        >
                    </div>

                    <!-- GPS Point -->
                    <div class="form-group">
                        <label for="gps_point" class="form-label">GPS Coordinates</label>
                        <input 
                            type="text" 
                            id="gps_point" 
                            name="gps_point" 
                            class="form-input" 
                            value="<?= old('gps_point', $project['gps_point']) ?>"
                            placeholder="e.g., -6.2088, 106.8456"
                        >
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Latitude, Longitude format
                        </small>
                    </div>
                </div>
            </div>

            <!-- Files & Documentation Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); border-bottom: 2px solid var(--bg-tertiary); padding-bottom: var(--spacing-sm);">
                    📎 GPS Documentation (Optional)
                </h3>

                <!-- GPS KML File -->
                <div class="form-group">
                    <label for="gps_kml_file" class="form-label">GPS KML File</label>
                    <?php if ($project['gps_kml_path']): ?>
                        <div style="margin-bottom: var(--spacing-sm); padding: var(--spacing-sm); background: var(--bg-accent); border-radius: var(--radius-sm); font-size: 0.875rem;">
                            <strong>Current file:</strong> <?= basename($project['gps_kml_path']) ?>
                            <a href="<?= base_url($project['gps_kml_path']) ?>" target="_blank" style="margin-left: var(--spacing-sm); color: var(--brand-primary);">Download</a>
                        </div>
                    <?php endif; ?>
                    <input
                        type="file"
                        id="gps_kml_file"
                        name="gps_kml_file"
                        class="form-input"
                        accept=".kml,.kmz"
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Optional: Upload KML/KMZ geographic data files (max 15MB). Leave empty to keep current file.
                    </small>
                </div>
            </div>

            <!-- Status & Notes Section -->
            <div style="margin-bottom: var(--spacing-2xl);">
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); border-bottom: 2px solid var(--bg-tertiary); padding-bottom: var(--spacing-sm);">
                    📝 Status & Notes
                </h3>

                <!-- Status Notes -->
                <div class="form-group">
                    <label for="status_notes" class="form-label">Status Notes</label>
                    <textarea
                        id="status_notes"
                        name="status_notes"
                        class="form-input"
                        rows="4"
                        placeholder="Additional notes about the current project status"
                    ><?= old('status_notes', $project['status_notes']) ?></textarea>
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Optional notes about project status or progress
                    </small>
                </div>
            </div>

            <!-- Information Box -->
            <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin: var(--spacing-lg) 0;">
                <h4 style="color: var(--brand-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    📋 Project Update Notes
                </h4>
                <ul style="color: var(--text-secondary); font-size: 0.875rem; margin: 0; padding-left: var(--spacing-lg);">
                    <li>Changes will be saved immediately upon submission</li>
                    <li>Project code is automatically generated (format: PR{NN}{YYYY}) and cannot be changed</li>
                    <li>Initiation date is required and affects project code generation</li>
                    <li>File uploads will replace existing files if new ones are selected</li>
                    <li>Leave file fields empty to keep current files</li>
                    <li>Evaluation documents can be uploaded by evaluators</li>
                    <li>All fields marked with * are required</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center" style="margin-top: var(--spacing-xl);">
                <a href="<?= base_url('admin/projects') ?>" class="btn btn-secondary">
                    Cancel
                </a>

                <button type="submit" class="btn btn-primary">
                    Update Project
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Form Validation Styling -->
<style>
.project-edit-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.project-edit-form .form-input:invalid {
    border-color: var(--brand-danger);
}

.project-edit-form .form-input:valid {
    border-color: var(--brand-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .project-edit-form div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .d-flex.justify-content-between .btn {
        width: 100%;
        text-align: center;
    }
}
</style>

<script>
// Dependent dropdown functionality
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country_id');
    const provinceSelect = document.getElementById('province_id');
    const districtSelect = document.getElementById('district_id');
    const llgSelect = document.getElementById('llg_id');

    // Store original options
    const originalProvinces = Array.from(provinceSelect.options);
    const originalDistricts = Array.from(districtSelect.options);
    const originalLlgs = Array.from(llgSelect.options);

    function clearSelect(selectElement, placeholder = 'Select...') {
        selectElement.innerHTML = `<option value="">${placeholder}</option>`;
    }

    function filterOptions(selectElement, originalOptions, filterField, filterValue) {
        clearSelect(selectElement);

        if (!filterValue) return;

        originalOptions.forEach(option => {
            if (option.value && option.getAttribute('data-' + filterField) == filterValue) {
                selectElement.appendChild(option.cloneNode(true));
            }
        });
    }

    // Country change handler
    countrySelect.addEventListener('change', function() {
        const countryId = this.value;

        // Clear dependent dropdowns
        clearSelect(provinceSelect, 'Select Province');
        clearSelect(districtSelect, 'Select District');
        clearSelect(llgSelect, 'Select LLG');

        if (countryId) {
            // Filter provinces based on selected country
            filterOptions(provinceSelect, originalProvinces, 'country-id', countryId);
        }
    });

    // Province change handler
    provinceSelect.addEventListener('change', function() {
        const provinceId = this.value;

        // Clear dependent dropdowns
        clearSelect(districtSelect, 'Select District');
        clearSelect(llgSelect, 'Select LLG');

        if (provinceId) {
            // Filter districts based on selected province
            filterOptions(districtSelect, originalDistricts, 'province-id', provinceId);
        }
    });

    // District change handler
    districtSelect.addEventListener('change', function() {
        const districtId = this.value;

        // Clear dependent dropdown
        clearSelect(llgSelect, 'Select LLG');

        if (districtId) {
            // Filter LLGs based on selected district
            filterOptions(llgSelect, originalLlgs, 'district-id', districtId);
        }
    });

    // Initialize dropdowns based on current selections (for edit mode)
    if (countrySelect.value) {
        filterOptions(provinceSelect, originalProvinces, 'country-id', countrySelect.value);

        // Wait a bit for provinces to load, then filter districts
        setTimeout(() => {
            if (provinceSelect.value) {
                filterOptions(districtSelect, originalDistricts, 'province-id', provinceSelect.value);

                // Wait a bit for districts to load, then filter LLGs
                setTimeout(() => {
                    if (districtSelect.value) {
                        filterOptions(llgSelect, originalLlgs, 'district-id', districtSelect.value);
                    }
                }, 100);
            }
        }, 100);
    }
});
</script>

<?= $this->endSection() ?>
