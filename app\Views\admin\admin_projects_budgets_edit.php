<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-secondary">
    ← Back to Budget List
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Budget Item
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update budget item for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Budget Form -->
<div class="card">
    <div class="card-header">
        💰 Budget Item Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/budgets/' . $budgetItem['id'] . '/edit') ?>" class="budget-edit-form">
            <?= csrf_field() ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Item Code -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="item_code" class="form-label">
                            Item Code <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="text"
                               id="item_code"
                               name="item_code"
                               class="form-input"
                               style="border: 2px solid var(--brand-danger);"
                               value="<?= old('item_code', $budgetItem['item_code']) ?>"
                               placeholder="e.g., BUD001, EQUIP, LABOR"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Unique identifier for this budget item (max 30 characters)
                        </small>
                        <?php if (isset($errors['item_code'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['item_code']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Description -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="description" class="form-label">
                            Description <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <textarea id="description"
                                  name="description"
                                  class="form-input"
                                  style="border: 2px solid var(--brand-danger); min-height: 100px; resize: vertical;"
                                  placeholder="Detailed description of the budget item..."
                                  required><?= old('description', $budgetItem['description']) ?></textarea>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Detailed description of this budget item (max 255 characters)
                        </small>
                        <?php if (isset($errors['description'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['description']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Planned Amount -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="amount_planned" class="form-label">
                            Planned Amount <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <div style="position: relative;">
                            <span style="position: absolute; left: var(--spacing-sm); top: 50%; transform: translateY(-50%); color: var(--text-muted); font-weight: 600;">$</span>
                            <input type="number"
                                   id="amount_planned"
                                   name="amount_planned"
                                   class="form-input"
                                   style="border: 2px solid var(--brand-danger); padding-left: var(--spacing-lg);"
                                   value="<?= old('amount_planned', $budgetItem['amount_planned']) ?>"
                                   placeholder="0.00"
                                   step="0.01"
                                   min="0.01"
                                   required>
                        </div>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Planned budget amount for this item
                        </small>
                        <?php if (isset($errors['amount_planned'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['amount_planned']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Status -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="status" class="form-label">
                            Status
                        </label>
                        <select id="status"
                                name="status"
                                class="form-input"
                                style="border: 2px solid var(--brand-secondary);">
                            <option value="active" <?= old('status', $budgetItem['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="removed" <?= old('status', $budgetItem['status']) === 'removed' ? 'selected' : '' ?>>Removed</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Current status of this budget item
                        </small>
                        <?php if (isset($errors['status'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['status']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Audit Information -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-md); border-radius: var(--radius-sm); margin-top: var(--spacing-lg);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            📋 Audit Information
                        </h4>
                        <div style="font-size: 0.75rem; color: var(--text-muted); line-height: 1.5;">
                            <div><strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($budgetItem['created_at'])) ?></div>
                            <?php if ($budgetItem['updated_at']): ?>
                                <div><strong>Last Updated:</strong> <?= date('M j, Y g:i A', strtotime($budgetItem['updated_at'])) ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    💰 Update Budget Item
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 1rem;">Validation Errors</h4>
        <ul style="margin: 0; padding-left: var(--spacing-md);">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li style="font-size: 0.875rem;"><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
    }
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});

// Form validation
document.querySelector('.budget-edit-form').addEventListener('submit', function(e) {
    const itemCode = document.getElementById('item_code').value.trim();
    const description = document.getElementById('description').value.trim();
    const amountPlanned = document.getElementById('amount_planned').value;

    if (!itemCode || !description || !amountPlanned || parseFloat(amountPlanned) <= 0) {
        e.preventDefault();
        alert('Please fill in all required fields with valid values.');
        return false;
    }
});
</script>

<?= $this->endSection() ?>
