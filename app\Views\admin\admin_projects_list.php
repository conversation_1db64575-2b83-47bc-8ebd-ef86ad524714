<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/create') ?>" class="btn btn-primary">
    📁 Create New Project
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Management
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Manage projects for <strong><?= esc($admin_organization_name) ?></strong>
        </p>
    </div>
</div>

<!-- Filters -->
<div class="card mb-xl">
    <div class="card-header">
        Filters & Search
    </div>
    <div style="padding: var(--spacing-lg);">
        <form method="get" action="<?= base_url('admin/projects') ?>" class="d-flex gap-md align-items-center" style="flex-wrap: wrap;">
            
            <!-- Search -->
            <div style="flex: 1; min-width: 200px;">
                <input 
                    type="text" 
                    name="search" 
                    class="form-input" 
                    placeholder="Search projects..." 
                    value="<?= esc($filters['search']) ?>"
                >
            </div>

            <!-- Status Filter -->
            <div style="min-width: 150px;">
                <select name="status" class="form-input">
                    <option value="">All Status</option>
                    <option value="planning" <?= ($filters['status'] === 'planning') ? 'selected' : '' ?>>Planning</option>
                    <option value="active" <?= ($filters['status'] === 'active') ? 'selected' : '' ?>>Active</option>
                    <option value="on-hold" <?= ($filters['status'] === 'on-hold') ? 'selected' : '' ?>>On Hold</option>
                    <option value="completed" <?= ($filters['status'] === 'completed') ? 'selected' : '' ?>>Completed</option>
                    <option value="cancelled" <?= ($filters['status'] === 'cancelled') ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>

            <!-- Organization Context (Auto-filtered) -->
            <div style="min-width: 200px; display: flex; align-items: center; gap: var(--spacing-sm); background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-sm) var(--spacing-md);">
                <div style="width: 30px; height: 30px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; font-size: 0.875rem;">
                    🏢
                </div>
                <div>
                    <div style="font-weight: 600; color: var(--brand-primary); font-size: 0.875rem;">
                        <?= esc($admin_organization_name) ?>
                    </div>
                    <div style="color: var(--text-muted); font-size: 0.75rem;">
                        Your Organization
                    </div>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="d-flex gap-md">
                <button type="submit" class="btn btn-primary">Filter</button>
                <a href="<?= base_url('admin/projects') ?>" class="btn btn-secondary">Clear</a>
            </div>
        </form>
    </div>
</div>

<!-- Projects Table -->
<div class="card">
    <div class="card-header">
        Projects (<?= count($projects) ?> found)
    </div>
    
    <?php if (!empty($projects)): ?>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Project</th>
                        <th>Status</th>
                        <th>Location</th>
                        <th>Timeline</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($projects as $project): ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                        📁
                                    </div>
                                    <div>
                                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                            <?= esc($project['title']) ?>
                                        </div>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                                            <?= esc($project['pro_code']) ?>
                                            <?php if ($project['goal']): ?>
                                                • <?= esc(substr($project['goal'], 0, 50)) ?><?= strlen($project['goal']) > 50 ? '...' : '' ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $statusColors = [
                                    'planning' => 'var(--text-muted)',
                                    'active' => 'var(--brand-secondary)',
                                    'on-hold' => 'var(--brand-warning)',
                                    'completed' => 'var(--brand-primary)',
                                    'cancelled' => 'var(--brand-danger)'
                                ];
                                $statusColor = $statusColors[$project['status']] ?? 'var(--text-muted)';
                                ?>
                                <span style="background: <?= $statusColor ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?= esc($project['status']) ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    <?php if ($project['country_name'] || $project['province_name'] || $project['district_name'] || $project['llg_name']): ?>
                                        <?php
                                        $locationParts = array_filter([
                                            $project['llg_name'],
                                            $project['district_name'],
                                            $project['province_name'],
                                            $project['country_name']
                                        ]);
                                        echo esc(implode(', ', $locationParts));
                                        ?>
                                    <?php else: ?>
                                        <span style="color: var(--text-muted);">No location set</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    <?php if ($project['start_date'] && $project['end_date']): ?>
                                        <div><?= date('M j, Y', strtotime($project['start_date'])) ?></div>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">to <?= date('M j, Y', strtotime($project['end_date'])) ?></div>
                                    <?php elseif ($project['start_date']): ?>
                                        <div>Started: <?= date('M j, Y', strtotime($project['start_date'])) ?></div>
                                    <?php elseif ($project['initiation_date']): ?>
                                        <div>Initiated: <?= date('M j, Y', strtotime($project['initiation_date'])) ?></div>
                                    <?php else: ?>
                                        <span style="color: var(--text-muted);">No timeline set</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span style="color: var(--text-secondary); font-size: 0.875rem;">
                                    <?= date('M j, Y', strtotime($project['created_at'])) ?>
                                </span>
                                <?php if ($project['creator_name']): ?>
                                    <div style="font-size: 0.75rem; color: var(--text-muted);">
                                        by <?= esc($project['creator_name']) ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="d-flex gap-md">
                                    <a
                                        href="<?= base_url('admin/projects/' . $project['id']) ?>"
                                        class="btn btn-secondary"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="View Details"
                                    >
                                        👁️ View
                                    </a>
                                    <a
                                        href="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>"
                                        class="btn btn-primary"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Edit Project"
                                    >
                                        ✏️ Edit
                                    </a>
                                    <button
                                        onclick="showDeleteModal(<?= $project['id'] ?>, '<?= esc($project['title']) ?>')"
                                        class="btn btn-danger"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Delete Project"
                                    >
                                        🗑️ Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
            <p style="font-size: 1.125rem; margin-bottom: var(--spacing-md);">No projects found</p>
            <p>Try adjusting your filters or create a new project.</p>
            <a href="<?= base_url('admin/projects/create') ?>" class="btn btn-primary" style="margin-top: var(--spacing-md);">
                Create First Project
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Delete Project</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete the project <strong id="deleteProjectName"></strong>?
        </p>
        <p style="color: var(--brand-danger); font-size: 0.875rem; margin-bottom: var(--spacing-lg);">
            This action cannot be undone. All project data will be permanently removed.
        </p>
        
        <form id="deleteForm" method="post" style="display: none;">
            <?= csrf_field() ?>
        </form>
        
        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDelete()" class="btn btn-danger">Delete Project</button>
        </div>
    </div>
</div>

<script>
let currentDeleteProjectId = null;

function showDeleteModal(projectId, projectName) {
    currentDeleteProjectId = projectId;
    document.getElementById('deleteProjectName').textContent = projectName;
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    currentDeleteProjectId = null;
}

function confirmDelete() {
    if (currentDeleteProjectId) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= base_url('admin/projects/') ?>' + currentDeleteProjectId + '/delete';
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<?= $this->endSection() ?>
