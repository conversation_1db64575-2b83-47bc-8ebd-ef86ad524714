<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Budget List</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Create Budget Item
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Add a new budget item to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Budget Form -->
<div class="card">
    <div class="card-header">
        💰 Budget Item Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/budgets/create') ?>" class="budget-create-form">
            <?= csrf_field() ?>

            <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Item Code -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="item_code" class="form-label">
                            Item Code <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="text"
                               id="item_code"
                               name="item_code"
                               class="form-input"
                               style="border: 2px solid var(--brand-danger);"
                               value="<?= old('item_code') ?>"
                               placeholder="e.g., BUD001, EQUIP, LABOR"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Unique identifier for this budget item (max 30 characters)
                        </small>
                        <?php if (isset($errors['item_code'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['item_code']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Description -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="description" class="form-label">
                            Description <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <textarea id="description"
                                  name="description"
                                  class="form-input"
                                  style="border: 2px solid var(--brand-danger); min-height: 100px; resize: vertical;"
                                  placeholder="Detailed description of the budget item..."
                                  required><?= old('description') ?></textarea>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Detailed description of this budget item (max 255 characters)
                        </small>
                        <?php if (isset($errors['description'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['description']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Planned Amount -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="amount_planned" class="form-label">
                            Planned Amount <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <div style="position: relative;">
                            <span style="position: absolute; left: var(--spacing-sm); top: 50%; transform: translateY(-50%); color: var(--text-muted); font-weight: 600;">$</span>
                            <input type="number"
                                   id="amount_planned"
                                   name="amount_planned"
                                   class="form-input"
                                   style="border: 2px solid var(--brand-danger); padding-left: var(--spacing-lg);"
                                   value="<?= old('amount_planned') ?>"
                                   placeholder="0.00"
                                   step="0.01"
                                   min="0.01"
                                   required>
                        </div>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Planned budget amount for this item
                        </small>
                        <?php if (isset($errors['amount_planned'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['amount_planned']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Status -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="status" class="form-label">
                            Status
                        </label>
                        <select id="status"
                                name="status"
                                class="form-input"
                                style="border: 2px solid var(--brand-secondary);">
                            <option value="active" <?= old('status', 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="removed" <?= old('status') === 'removed' ? 'selected' : '' ?>>Removed</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Current status of this budget item (defaults to Active)
                        </small>
                        <?php if (isset($errors['status'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['status']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-secondary btn-mobile">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">Cancel</span>
                </a>
                <button type="submit" class="btn btn-primary btn-mobile">
                    <span class="btn-icon">💰</span>
                    <span class="btn-text">Create Budget Item</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 1rem;">Validation Errors</h4>
        <ul style="margin: 0; padding-left: var(--spacing-md);">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li style="font-size: 0.875rem;"><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 44px;
    border: 2px solid var(--brand-success); /* Default green for optional fields */
}

/* Required fields have red outline */
.form-input[required] {
    border: 2px solid var(--brand-danger);
}

.form-input:focus {
    outline: none;
    border-width: 2px;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Enhanced select dropdowns */
select.form-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Enhanced textareas */
textarea.form-input {
    resize: vertical;
    min-height: 100px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .form-input {
        min-height: 48px;
        font-size: 16px;
        padding: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    .form-actions {
        gap: var(--spacing-sm);
    }

    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Loading state for submit button */
.btn-mobile.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-mobile.loading .btn-text::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});

// Form validation
document.querySelector('.budget-create-form').addEventListener('submit', function(e) {
    const itemCode = document.getElementById('item_code').value.trim();
    const description = document.getElementById('description').value.trim();
    const amountPlanned = document.getElementById('amount_planned').value;

    if (!itemCode || !description || !amountPlanned || parseFloat(amountPlanned) <= 0) {
        e.preventDefault();
        alert('Please fill in all required fields with valid values.');
        return false;
    }
});
</script>

<?= $this->endSection() ?>
