<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Create New Milestone
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Add a new milestone to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Milestone Form -->
<div class="card">
    <div class="card-header">
        🎯 Milestone Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/milestones/create') ?>" class="milestone-create-form">
            <?= csrf_field() ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">
                
                <!-- Left Column -->
                <div>
                    <!-- Phase Selection -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="phase_id" class="form-label">
                            Phase <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="phase_id" 
                                name="phase_id" 
                                class="form-input"
                                required>
                            <option value="">Select Phase</option>
                            <?php foreach ($phases as $phase): ?>
                                <option value="<?= $phase['id'] ?>" <?= old('phase_id') == $phase['id'] ? 'selected' : '' ?>>
                                    <?= esc($phase['title']) ?> (<?= esc($phase['phase_code']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Select the phase this milestone belongs to
                        </small>
                    </div>

                    <!-- Milestone Code -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="milestone_code" class="form-label">
                            Milestone Code <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="text" 
                               id="milestone_code" 
                               name="milestone_code" 
                               class="form-input"
                               value="<?= old('milestone_code') ?>"
                               placeholder="e.g., M001, DESIGN, REVIEW"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Unique identifier for this milestone (max 20 characters)
                        </small>
                    </div>

                    <!-- Milestone Title -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="title" class="form-label">
                            Milestone Title <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               class="form-input"
                               value="<?= old('title') ?>"
                               placeholder="e.g., Design Approval, Site Survey Complete"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Descriptive name for this milestone (max 200 characters)
                        </small>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Target Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="target_date" class="form-label">
                            Target Date
                        </label>
                        <input type="date" 
                               id="target_date" 
                               name="target_date" 
                               class="form-input"
                               value="<?= old('target_date') ?>">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            When this milestone should be completed
                        </small>
                    </div>

                    <!-- Status -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="status" class="form-label">
                            Status <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="status" 
                                name="status" 
                                class="form-input"
                                required>
                            <option value="">Select Status</option>
                            <option value="pending" <?= old('status') === 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="in-progress" <?= old('status') === 'in-progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="completed" <?= old('status') === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="approved" <?= old('status') === 'approved' ? 'selected' : '' ?>>Approved</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Current status of this milestone
                        </small>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="description" class="form-label">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          class="form-input"
                          rows="4"
                          placeholder="Detailed description of this milestone, deliverables, and acceptance criteria..."><?= old('description') ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Optional detailed description of the milestone
                </small>
            </div>

            <!-- Form Actions -->
            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                <a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    ✅ Create Milestone
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Form validation styling - matches project edit form */
.milestone-create-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.milestone-create-form .form-input:invalid {
    border-color: var(--brand-danger);
}

.milestone-create-form .form-input:valid {
    border-color: var(--brand-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .milestone-create-form div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
}
</style>

<?= $this->endSection() ?>
