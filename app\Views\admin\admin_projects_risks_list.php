<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
    ← Back to Project
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/create') ?>" class="btn btn-primary">
    ➕ Add New Risk
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Risks
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Managing risks for: <strong><?= esc($project['title']) ?></strong> (<?= esc($project['pro_code']) ?>)
        </p>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success mb-lg">
        <?= session()->getFlashdata('success') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger mb-lg">
        <?= session()->getFlashdata('error') ?>
    </div>
<?php endif; ?>

<!-- Project Risks List -->
<div class="card">
    <div class="card-header">
        <div style="display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <span>⚠️</span>
                <span>Project Risks</span>
            </div>
            <div style="margin-left: auto; display: flex; gap: var(--spacing-sm);">
                <span style="font-size: 0.75rem; color: var(--text-muted);">
                    Total: <?= count($risks) ?> risks
                </span>
            </div>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($risks)): ?>
            <div style="display: grid; gap: var(--spacing-lg);">
                <?php foreach ($risks as $risk): ?>
                    <div class="card" style="border: 1px solid var(--border-color);">
                        <div style="padding: var(--spacing-lg);">
                            <div style="display: flex; justify-content: between; align-items: start; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                                <div style="flex: 1;">
                                    <h3 style="color: var(--text-primary); margin: 0 0 var(--spacing-sm) 0; font-size: 1.1rem; font-weight: 600;">
                                        Risk Description
                                    </h3>
                                    <p style="color: var(--text-secondary); margin: 0; line-height: 1.5;">
                                        <?= esc($risk['description']) ?>
                                    </p>
                                </div>
                                
                                <!-- Risk Actions -->
                                <div style="display: flex; gap: var(--spacing-sm);">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/' . $risk['id'] . '/edit') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        ✏️ Edit
                                    </a>
                                    <button onclick="showDeleteRiskModal(<?= $risk['id'] ?>, '<?= esc(substr($risk['description'], 0, 50)) ?>...')" class="btn btn-danger" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        🗑️ Delete
                                    </button>
                                </div>
                            </div>

                            <!-- Risk Details Grid -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md); margin-top: var(--spacing-md); padding-top: var(--spacing-md); border-top: 1px solid var(--border-light);">
                                <!-- Risk Type -->
                                <div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Risk Type</div>
                                    <span class="badge badge-<?= $risk['risk_type'] === 'witnessed' ? 'danger' : ($risk['risk_type'] === 'foreseen' ? 'warning' : 'info') ?>">
                                        <?= ucfirst($risk['risk_type']) ?>
                                    </span>
                                </div>

                                <!-- Risk Level -->
                                <div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Risk Level</div>
                                    <span class="badge badge-<?= $risk['risk_level'] === 'critical' ? 'danger' : ($risk['risk_level'] === 'high' ? 'warning' : ($risk['risk_level'] === 'medium' ? 'info' : 'secondary')) ?>">
                                        <?= ucfirst($risk['risk_level']) ?>
                                    </span>
                                </div>

                                <!-- Approval Status -->
                                <div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Status</div>
                                    <span class="badge badge-<?= $risk['approval_status'] === 'approved' ? 'success' : ($risk['approval_status'] === 'rejected' ? 'danger' : 'warning') ?>">
                                        <?= ucfirst($risk['approval_status']) ?>
                                    </span>
                                </div>

                                <!-- Risk Score -->
                                <div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Risk Score</div>
                                    <?php
                                    $riskScores = ['low' => 1, 'medium' => 2, 'high' => 3, 'critical' => 4];
                                    $riskScore = $riskScores[$risk['risk_level']] ?? 1;
                                    ?>
                                    <span class="badge badge-<?= $riskScore >= 4 ? 'danger' : ($riskScore >= 3 ? 'warning' : ($riskScore >= 2 ? 'info' : 'secondary')) ?>">
                                        <?= $riskScore ?>/4
                                    </span>
                                </div>
                            </div>

                            <!-- Mitigation Strategy -->
                            <?php if (!empty($risk['mitigation'])): ?>
                                <div style="margin-top: var(--spacing-md); padding-top: var(--spacing-md); border-top: 1px solid var(--border-light);">
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Mitigation Strategy</div>
                                    <p style="color: var(--text-secondary); margin: 0; font-size: 0.9rem; line-height: 1.4;">
                                        <?= esc($risk['mitigation']) ?>
                                    </p>
                                </div>
                            <?php endif; ?>

                            <!-- Timestamps -->
                            <div style="margin-top: var(--spacing-md); padding-top: var(--spacing-md); border-top: 1px solid var(--border-light); font-size: 0.75rem; color: var(--text-muted);">
                                Created: <?= date('M j, Y g:i A', strtotime($risk['created_at'])) ?>
                                <?php if ($risk['updated_at'] !== $risk['created_at']): ?>
                                    • Updated: <?= date('M j, Y g:i A', strtotime($risk['updated_at'])) ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">⚠️</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Risks Identified</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start managing project risks by identifying potential issues.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/create') ?>" class="btn btn-primary">
                    ⚠️ Add First Risk
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Risk Modal -->
<div id="deleteRiskModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Delete Risk</h3>
            <span class="modal-close" onclick="closeDeleteRiskModal()">&times;</span>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to delete this risk?</p>
            <p><strong id="riskDescription"></strong></p>
            <p style="color: var(--text-muted); font-size: 0.9rem;">This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeDeleteRiskModal()">Cancel</button>
            <form id="deleteRiskForm" method="post" style="display: inline;">
                <button type="submit" class="btn btn-danger">Delete Risk</button>
            </form>
        </div>
    </div>
</div>

<script>
function showDeleteRiskModal(riskId, description) {
    document.getElementById('riskDescription').textContent = description;
    document.getElementById('deleteRiskForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/risks/') ?>' + riskId + '/delete';
    document.getElementById('deleteRiskModal').style.display = 'flex';
}

function closeDeleteRiskModal() {
    document.getElementById('deleteRiskModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('deleteRiskModal');
    if (event.target === modal) {
        closeDeleteRiskModal();
    }
}
</script>

<?= $this->endSection() ?>
