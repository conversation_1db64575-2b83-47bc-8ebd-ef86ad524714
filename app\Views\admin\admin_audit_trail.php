<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/audit') ?>?<?= http_build_query($filters) ?>&export=csv" class="btn btn-secondary">
    📊 Export CSV
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Audit Trail
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Monitor system activities and user actions
        </p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-2xl);">
    
    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.5rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['total_logs']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Total Logs
                </p>
            </div>
            <div style="width: 50px; height: 50px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                📋
            </div>
        </div>
    </div>

    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.5rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['logs_today']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Today's Activity
                </p>
            </div>
            <div style="width: 50px; height: 50px; background: var(--gradient-secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                📅
            </div>
        </div>
    </div>

    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.5rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['logs_this_week']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    This Week
                </p>
            </div>
            <div style="width: 50px; height: 50px; background: var(--gradient-accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                📊
            </div>
        </div>
    </div>

    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    Most Active
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.75rem;">
                    <?= esc($stats['most_active_user']) ?>
                </p>
            </div>
            <div style="width: 50px; height: 50px; background: var(--gradient-secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                👑
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-xl">
    <div class="card-header">
        Filters & Search
    </div>
    <div style="padding: var(--spacing-lg);">
        <form method="get" action="<?= base_url('admin/audit') ?>" class="audit-filters">
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                
                <!-- Search -->
                <div>
                    <label class="form-label">Search</label>
                    <input 
                        type="text" 
                        name="search" 
                        class="form-input" 
                        placeholder="Search descriptions, users..." 
                        value="<?= esc($filters['search']) ?>"
                    >
                </div>

                <!-- User Filter -->
                <div>
                    <label class="form-label">User</label>
                    <select name="user" class="form-input">
                        <option value="">All Users</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?= $user['user_id'] ?>" <?= ($filters['user'] == $user['user_id']) ? 'selected' : '' ?>>
                                <?= esc($user['username']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Action Filter -->
                <div>
                    <label class="form-label">Action</label>
                    <select name="action" class="form-input">
                        <option value="">All Actions</option>
                        <option value="create" <?= ($filters['action'] === 'create') ? 'selected' : '' ?>>Create</option>
                        <option value="update" <?= ($filters['action'] === 'update') ? 'selected' : '' ?>>Update</option>
                        <option value="delete" <?= ($filters['action'] === 'delete') ? 'selected' : '' ?>>Delete</option>
                        <option value="login" <?= ($filters['action'] === 'login') ? 'selected' : '' ?>>Login</option>
                        <option value="logout" <?= ($filters['action'] === 'logout') ? 'selected' : '' ?>>Logout</option>
                    </select>
                </div>

                <!-- Module Filter -->
                <div>
                    <label class="form-label">Module</label>
                    <select name="module" class="form-input">
                        <option value="">All Modules</option>
                        <?php foreach ($modules as $module): ?>
                            <option value="<?= $module['module'] ?>" <?= ($filters['module'] === $module['module']) ? 'selected' : '' ?>>
                                <?= esc(ucfirst(str_replace('_', ' ', $module['module']))) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                
                <!-- Date From -->
                <div>
                    <label class="form-label">Date From</label>
                    <input 
                        type="date" 
                        name="date_from" 
                        class="form-input" 
                        value="<?= esc($filters['date_from']) ?>"
                    >
                </div>

                <!-- Date To -->
                <div>
                    <label class="form-label">Date To</label>
                    <input 
                        type="date" 
                        name="date_to" 
                        class="form-input" 
                        value="<?= esc($filters['date_to']) ?>"
                    >
                </div>

                <!-- Per Page -->
                <div>
                    <label class="form-label">Per Page</label>
                    <select name="per_page" class="form-input">
                        <option value="25" <?= ($filters['per_page'] == 25) ? 'selected' : '' ?>>25</option>
                        <option value="50" <?= ($filters['per_page'] == 50) ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= ($filters['per_page'] == 100) ? 'selected' : '' ?>>100</option>
                        <option value="200" <?= ($filters['per_page'] == 200) ? 'selected' : '' ?>>200</option>
                    </select>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="d-flex gap-md">
                <button type="submit" class="btn btn-primary">Apply Filters</button>
                <a href="<?= base_url('admin/audit') ?>" class="btn btn-secondary">Clear All</a>
            </div>
        </form>
    </div>
</div>

<!-- Audit Logs Table -->
<div class="card">
    <div class="card-header">
        Audit Logs (<?= count($audit_logs) ?> shown)
    </div>
    
    <?php if (!empty($audit_logs)): ?>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Date/Time</th>
                        <th>User</th>
                        <th>Action</th>
                        <th>Details</th>
                        <th>IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($audit_logs as $log): ?>
                        <tr>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    <?= date('M j, Y', strtotime($log['created_at'])) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?= date('g:i:s A', strtotime($log['created_at'])) ?>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                    <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.75rem;">
                                        <?= strtoupper(substr($log['username'] ?: 'S', 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div style="font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">
                                            <?= esc($log['username'] ?: 'System') ?>
                                        </div>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                                            <?= esc($log['user_type']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $actionColors = [
                                    'create' => 'var(--brand-secondary)',
                                    'update' => 'var(--brand-primary)',
                                    'delete' => 'var(--brand-danger)',
                                    'login' => 'var(--brand-secondary)',
                                    'logout' => 'var(--text-muted)'
                                ];
                                $actionColor = $actionColors[$log['action']] ?? 'var(--text-muted)';
                                ?>
                                <span style="background: <?= $actionColor ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?= esc($log['action']) ?>
                                </span>
                                <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                                    <?= esc(ucfirst(str_replace('_', ' ', $log['module']))) ?>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    <?= esc($log['description']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    Table: <?= esc($log['table_name']) ?> | ID: <?= esc($log['primary_key']) ?>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    <?= esc($log['ip_address']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?= esc(substr($log['user_agent'], 0, 30)) ?>...
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📋</div>
            <p style="font-size: 1.125rem; margin-bottom: var(--spacing-md);">No audit logs found</p>
            <p>Try adjusting your filters or check back later.</p>
        </div>
    <?php endif; ?>
</div>

<?= $this->endSection() ?>
