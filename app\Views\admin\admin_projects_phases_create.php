<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Project</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Create New Phase
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Add a new phase to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Phase Form -->
<div class="card">
    <div class="card-header">
        📋 Phase Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/phases/create') ?>" class="phase-create-form">
            <?= csrf_field() ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Phase Code -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="phase_code" class="form-label">
                            Phase Code <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="text"
                               id="phase_code"
                               name="phase_code"
                               class="form-input"
                               value="<?= old('phase_code') ?>"
                               placeholder="e.g., PH001, INIT, PLAN"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Unique identifier for this phase (max 20 characters)
                        </small>
                    </div>

                    <!-- Phase Title -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="title" class="form-label">
                            Phase Title <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="text"
                               id="title"
                               name="title"
                               class="form-input"
                               value="<?= old('title') ?>"
                               placeholder="e.g., Planning Phase, Implementation Phase"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Descriptive name for this phase (max 150 characters)
                        </small>
                    </div>

                    <!-- Status -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="status" class="form-label">
                            Status <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="status"
                                name="status"
                                class="form-input"
                                required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="deactivated" <?= old('status') === 'deactivated' ? 'selected' : '' ?>>Deactivated</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Current status of this phase
                        </small>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Start Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="start_date" class="form-label">
                            Start Date
                        </label>
                        <input type="date"
                               id="start_date"
                               name="start_date"
                               class="form-input"
                               value="<?= old('start_date') ?>">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            When this phase is scheduled to start
                        </small>
                    </div>

                    <!-- End Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="end_date" class="form-label">
                            End Date
                        </label>
                        <input type="date"
                               id="end_date"
                               name="end_date"
                               class="form-input"
                               value="<?= old('end_date') ?>">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            When this phase is scheduled to end
                        </small>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="description" class="form-label">
                    Description
                </label>
                <textarea id="description"
                          name="description"
                          class="form-input"
                          rows="4"
                          placeholder="Detailed description of this phase, its objectives, and key activities..."><?= old('description') ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Optional detailed description of the phase
                </small>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary btn-mobile">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">Cancel</span>
                </a>
                <button type="submit" class="btn btn-primary btn-mobile">
                    <span class="btn-icon">✅</span>
                    <span class="btn-text">Create Phase</span>
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Form validation styling - matches project edit form */
.phase-create-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.phase-create-form .form-input:invalid {
    border-color: var(--brand-danger);
}

.phase-create-form .form-input:valid {
    border-color: var(--brand-secondary);
}

/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Enhanced form inputs for mobile */
.phase-create-form .form-input {
    min-height: 44px;
    font-size: 0.875rem; /* Original size */
    padding: var(--spacing-md);
}

.phase-create-form select.form-input {
    min-height: 44px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.phase-create-form textarea.form-input {
    resize: vertical;
    min-height: 100px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .phase-create-form div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 0.875rem;
        min-height: 48px; /* Larger touch target on mobile only */
    }

    /* Larger touch targets on mobile */
    .phase-create-form .form-input {
        padding: var(--spacing-md);
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 48px; /* Larger touch target on mobile only */
    }

    /* Better spacing on mobile */
    .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    /* Stack form actions vertically on very small screens */
    .form-actions {
        gap: var(--spacing-sm);
    }

    /* Adjust card padding for small screens */
    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

.phase-create-form .form-input:focus {
    border-width: 2px;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading state for submit button */
.btn-mobile.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-mobile.loading .btn-text::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}
</style>

<?= $this->endSection() ?>
