<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-secondary">
    ← Back to Risks
</a>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
    👁️ View Project
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Risk
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Updating risk for: <strong><?= esc($project['title']) ?></strong> (<?= esc($project['pro_code']) ?>)
        </p>
    </div>
</div>

<!-- Edit Risk Form -->
<div class="card">
    <div class="card-header">
        ⚠️ Risk Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/risks/' . $risk['id'] . '/edit') ?>" class="risk-edit-form">
            <?= csrf_field() ?>

            <!-- Risk Description -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="description" class="form-label">
                    Risk Description <span style="color: var(--brand-danger);">*</span>
                </label>
                <textarea id="description"
                          name="description"
                          class="form-input"
                          style="border: 2px solid var(--brand-danger); min-height: 120px; resize: vertical;"
                          placeholder="Describe the potential risk in detail..."
                          required><?= old('description', $risk['description']) ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Clear description of what could go wrong and its potential impact
                </small>
                <?php if (isset($errors['description'])): ?>
                    <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                        <?= esc($errors['description']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column - Risk Classification -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        📋 Risk Classification
                    </h3>

                    <!-- Risk Type -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="risk_type" class="form-label">
                            Risk Type <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="risk_type"
                                name="risk_type"
                                class="form-input"
                                style="border: 2px solid var(--brand-danger);"
                                required>
                            <option value="">Select Risk Type</option>
                            <option value="proposed" <?= old('risk_type', $risk['risk_type']) === 'proposed' ? 'selected' : '' ?>>Proposed - Identified during planning</option>
                            <option value="foreseen" <?= old('risk_type', $risk['risk_type']) === 'foreseen' ? 'selected' : '' ?>>Foreseen - Anticipated during implementation</option>
                            <option value="witnessed" <?= old('risk_type', $risk['risk_type']) === 'witnessed' ? 'selected' : '' ?>>Witnessed - Actually occurred</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            When was this risk identified in the project lifecycle?
                        </small>
                        <?php if (isset($errors['risk_type'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['risk_type']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Risk Level -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="risk_level" class="form-label">
                            Risk Level <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="risk_level"
                                name="risk_level"
                                class="form-input"
                                style="border: 2px solid var(--brand-danger);"
                                required>
                            <option value="">Select Risk Level</option>
                            <option value="low" <?= old('risk_level', $risk['risk_level']) === 'low' ? 'selected' : '' ?>>Low - Minor impact</option>
                            <option value="medium" <?= old('risk_level', $risk['risk_level']) === 'medium' ? 'selected' : '' ?>>Medium - Moderate impact</option>
                            <option value="high" <?= old('risk_level', $risk['risk_level']) === 'high' ? 'selected' : '' ?>>High - Significant impact</option>
                            <option value="critical" <?= old('risk_level', $risk['risk_level']) === 'critical' ? 'selected' : '' ?>>Critical - Severe impact</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            How severely would this risk affect the project?
                        </small>
                        <?php if (isset($errors['risk_level'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['risk_level']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column - Management Information -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        🎯 Management Information
                    </h3>

                    <!-- Milestone Association -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="milestone_id" class="form-label">
                            Associated Milestone
                        </label>
                        <select id="milestone_id"
                                name="milestone_id"
                                class="form-input"
                                style="border: 2px solid var(--brand-success);">
                            <option value="">No specific milestone</option>
                            <?php foreach ($milestones as $milestone): ?>
                                <option value="<?= $milestone['id'] ?>" <?= old('milestone_id', $risk['milestone_id']) == $milestone['id'] ? 'selected' : '' ?>>
                                    <?= esc($milestone['milestone_code']) ?> - <?= esc($milestone['title']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Link this risk to a specific project milestone (optional)
                        </small>
                        <?php if (isset($errors['milestone_id'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['milestone_id']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Approval Status -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="approval_status" class="form-label">
                            Approval Status <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="approval_status"
                                name="approval_status"
                                class="form-input"
                                style="border: 2px solid var(--brand-danger);"
                                required>
                            <option value="">Select Status</option>
                            <option value="pending" <?= old('approval_status', $risk['approval_status']) === 'pending' ? 'selected' : '' ?>>Pending - Awaiting review</option>
                            <option value="approved" <?= old('approval_status', $risk['approval_status']) === 'approved' ? 'selected' : '' ?>>Approved - Confirmed risk</option>
                            <option value="rejected" <?= old('approval_status', $risk['approval_status']) === 'rejected' ? 'selected' : '' ?>>Rejected - Not a valid risk</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Current approval status of this risk
                        </small>
                        <?php if (isset($errors['approval_status'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['approval_status']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Mitigation Strategy -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="mitigation" class="form-label">
                    Mitigation Strategy
                </label>
                <textarea id="mitigation"
                          name="mitigation"
                          class="form-input"
                          style="border: 2px solid var(--brand-success); min-height: 100px; resize: vertical;"
                          placeholder="Describe how this risk can be prevented, reduced, or managed..."><?= old('mitigation', $risk['mitigation']) ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Optional. Describe preventive measures or response plans
                </small>
                <?php if (isset($errors['mitigation'])): ?>
                    <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                        <?= esc($errors['mitigation']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Risk Level Preview -->
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    📊 Risk Level Preview
                </h4>
                <div id="riskLevelPreview" style="font-size: 1.1rem; color: var(--text-secondary);">
                    Select risk level to see severity indicator
                </div>
                <small style="color: var(--text-muted); margin-top: var(--spacing-sm); display: block;">
                    Risk levels help prioritize attention and resources
                </small>
            </div>

            <!-- Risk History -->
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    📅 Risk History
                </h4>
                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                    <div style="margin-bottom: var(--spacing-xs);">
                        <strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($risk['created_at'])) ?>
                    </div>
                    <?php if ($risk['updated_at'] !== $risk['created_at']): ?>
                        <div>
                            <strong>Last Updated:</strong> <?= date('M j, Y g:i A', strtotime($risk['updated_at'])) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Form Actions -->
            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    💾 Update Risk
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Risk level preview
function updateRiskLevelPreview() {
    const riskLevel = document.getElementById('risk_level').value;
    const preview = document.getElementById('riskLevelPreview');

    if (riskLevel) {
        let levelClass = 'var(--text-secondary)';
        let levelLabel = '';

        switch(riskLevel) {
            case 'low':
                levelClass = 'var(--brand-success)';
                levelLabel = 'Low Risk - Minor impact, manageable';
                break;
            case 'medium':
                levelClass = 'var(--brand-warning)';
                levelLabel = 'Medium Risk - Moderate impact, requires attention';
                break;
            case 'high':
                levelClass = 'var(--brand-danger)';
                levelLabel = 'High Risk - Significant impact, needs immediate action';
                break;
            case 'critical':
                levelClass = 'var(--brand-danger)';
                levelLabel = 'Critical Risk - Severe impact, urgent intervention required';
                break;
        }

        preview.innerHTML = `<span style="color: ${levelClass}; font-weight: 600;">${levelLabel}</span>`;
    } else {
        preview.textContent = 'Select risk level to see severity indicator';
    }
}

// Add event listener
document.getElementById('risk_level').addEventListener('change', updateRiskLevelPreview);

// Initial calculation if values are pre-filled
document.addEventListener('DOMContentLoaded', updateRiskLevelPreview);
</script>

<?= $this->endSection() ?>
