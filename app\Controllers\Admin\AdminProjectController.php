<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectPhaseModel;
use App\Models\ProjectMilestoneModel;
use App\Models\ProjectBudgetItemModel;
use App\Models\ProjectRiskModel;
use App\Models\OrganizationModel;
use App\Models\CountryModel;
use App\Models\ProvinceModel;
use App\Models\DistrictModel;
use App\Models\LlgModel;
use App\Models\UserModel;

/**
 * Admin Project Controller
 * 
 * Handles CRUD operations for projects in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectController extends BaseController
{
    protected $projectModel;
    protected $projectPhaseModel;
    protected $projectMilestoneModel;
    protected $projectBudgetItemModel;
    protected $projectRiskModel;
    protected $organizationModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $userModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectPhaseModel = new ProjectPhaseModel();
        $this->projectMilestoneModel = new ProjectMilestoneModel();
        $this->projectBudgetItemModel = new ProjectBudgetItemModel();
        $this->projectRiskModel = new ProjectRiskModel();
        $this->organizationModel = new OrganizationModel();
        $this->countryModel = new CountryModel();
        $this->provinceModel = new ProvinceModel();
        $this->districtModel = new DistrictModel();
        $this->llgModel = new LlgModel();
        $this->userModel = new UserModel();
    }

    /**
     * Display projects list - GET request
     */
    public function index()
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminOrganizationName = session()->get('admin_organization_name');

        // Get filters from query parameters
        $filters = [
            'status' => $this->request->getGet('status'),
            'search' => $this->request->getGet('search'),
            'organization' => $adminOrganizationId // Automatically filter by admin's organization
        ];

        // Get projects with filters
        $projects = $this->getProjectsWithFilters($filters);

        $data = [
            'title' => 'Project Management - PROMIS Admin',
            'page_title' => 'Project Management',
            'projects' => $projects,
            'admin_organization_name' => $adminOrganizationName,
            'filters' => $filters
        ];

        return view('admin/admin_projects_list', $data);
    }

    /**
     * Show create project form - GET request
     */
    public function create()
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        // Get location data for dropdowns with relationships
        $countries = $this->countryModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();
        $provinces = $this->provinceModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();
        $districts = $this->districtModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();
        $llgs = $this->llgModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();

        $data = [
            'title' => 'Create New Project - PROMIS Admin',
            'page_title' => 'Create New Project',
            'countries' => $countries,
            'provinces' => $provinces,
            'districts' => $districts,
            'llgs' => $llgs
        ];

        return view('admin/admin_projects_create', $data);
    }

    /**
     * Store new project - POST request
     */
    public function store()
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Validation rules (made initiation_date required)
        $rules = [
            'title' => 'required|max_length[200]',
            'goal' => 'permit_empty|max_length[1000]',
            'description' => 'permit_empty',
            'initiation_date' => 'required|valid_date',
            'start_date' => 'permit_empty|valid_date',
            'end_date' => 'permit_empty|valid_date',
            'address_line' => 'permit_empty|max_length[255]',
            'country_id' => 'permit_empty|integer',
            'province_id' => 'permit_empty|integer',
            'district_id' => 'permit_empty|integer',
            'llg_id' => 'permit_empty|integer',
            'ward_name' => 'permit_empty|max_length[100]',
            'village_name' => 'permit_empty|max_length[100]',
            'gps_point' => 'permit_empty|max_length[100]',
            'status' => 'required|in_list[planning,active,on-hold,completed,cancelled]',
            'status_notes' => 'permit_empty',
            'baseline_year' => 'permit_empty|integer|greater_than[1900]|less_than_equal_to[2100]',
            'target_year' => 'permit_empty|integer|greater_than[1900]|less_than_equal_to[2100]',
            'gps_kml_file' => 'permit_empty|uploaded[gps_kml_file]|max_size[gps_kml_file,15360]|ext_in[gps_kml_file,kml,kmz]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for insertion
        // Ensure proper data types to prevent array-to-string conversion
        $projectData = ['org_id' => $adminOrganizationId, 'created_by' => $adminUserId];

        // String fields - ensure they are strings, not arrays
        $stringFields = ['other_project_ids', 'title', 'goal', 'description', 'address_line', 'ward_name', 'village_name', 'gps_point', 'status', 'status_notes'];
        foreach ($stringFields as $field) {
            $value = $this->request->getPost($field);
            if (is_array($value)) {
                $projectData[$field] = ''; // Convert arrays to empty string
            } else {
                $projectData[$field] = (string)($value ?: '');
            }
        }

        // Date fields - can be null or valid date string
        $dateFields = ['initiation_date', 'start_date', 'end_date'];
        foreach ($dateFields as $field) {
            $value = $this->request->getPost($field);
            if (is_array($value) || empty($value)) {
                $projectData[$field] = null;
            } else {
                $projectData[$field] = (string)$value;
            }
        }

        // Integer fields - can be null or integer
        $intFields = ['country_id', 'province_id', 'district_id', 'llg_id', 'baseline_year', 'target_year'];
        foreach ($intFields as $field) {
            $value = $this->request->getPost($field);
            if (is_array($value) || empty($value)) {
                $projectData[$field] = null;
            } else {
                $projectData[$field] = (int)$value;
            }
        }

        // Handle file uploads (only GPS KML file during creation)
        $projectData = $this->handleFileUploads($projectData);

        try {
            // Generate project code before insertion
            $initiationDate = $this->request->getPost('initiation_date');
            $initiationYear = date('Y', strtotime($initiationDate));
            $projectCode = $this->generateProjectCode($initiationYear);

            // Add project code to data
            $projectData['pro_code'] = $projectCode;

            $projectId = $this->projectModel->insert($projectData);

            if ($projectId) {
                return redirect()->to(base_url('admin/projects'))
                               ->with('success', 'Project created successfully! Project Code: ' . $projectCode);
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating project: ' . $e->getMessage());
        }
    }

    /**
     * Display specific project - GET request
     */
    public function show($id)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $id)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get related data
        $project = $this->enrichProjectData($project);

        // Get project phases with milestone counts
        $phases = $this->projectPhaseModel->getPhasesWithMilestoneCounts($id);

        // Get milestones with phase information for the project
        $milestones = $this->projectMilestoneModel->getMilestonesWithPhase($id);

        // Get budget items and statistics
        $budgetItems = $this->projectBudgetItemModel->getByProject($id, true); // Only active items
        $budgetStats = $this->projectBudgetItemModel->getBudgetStatistics($id);

        // Get project outcomes data from the dedicated controller
        $outcomeController = new \App\Controllers\Admin\AdminProjectOutcomeController();
        $outcomeData = $outcomeController->getOutcomesForProjectShow($id);

        // Get project issues addressed data from the dedicated controller
        $issueController = new \App\Controllers\Admin\AdminProjectIssueController();
        $issueData = $issueController->getIssuesForProjectShow($id);

        // Get project impact indicators data from the dedicated controller
        $indicatorController = new \App\Controllers\Admin\AdminProjectIndicatorController();
        $indicatorData = $indicatorController->getIndicatorsForProjectShow($id);

        // Get project risks
        $risks = $this->projectRiskModel->getByProject($id);

        $data = [
            'title' => 'Project Profile - PROMIS Admin',
            'page_title' => 'Project Profile',
            'project' => $project,
            'phases' => $phases,
            'milestones' => $milestones,
            'budgetItems' => $budgetItems,
            'budgetStats' => $budgetStats,
            'outcomes' => $outcomeData['outcomes'],
            'outcomeStats' => $outcomeData['outcomeStats'],
            'issues' => $issueData['issues'],
            'issueStats' => $issueData['issueStats'],
            'indicators' => $indicatorData['indicators'],
            'indicatorStats' => $indicatorData['indicatorStats'],
            'indicatorSummary' => $indicatorData['indicatorSummary'],
            'risks' => $risks
        ];

        return view('admin/admin_projects_show', $data);
    }

    /**
     * Show edit project form - GET request
     */
    public function edit($id)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $id)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get location data for dropdowns
        $countries = $this->countryModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();
        $provinces = $this->provinceModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();
        $districts = $this->districtModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();
        $llgs = $this->llgModel->where('deleted_at', null)->orderBy('name', 'ASC')->findAll();

        $data = [
            'title' => 'Edit Project - PROMIS Admin',
            'page_title' => 'Edit Project',
            'project' => $project,
            'countries' => $countries,
            'provinces' => $provinces,
            'districts' => $districts,
            'llgs' => $llgs
        ];

        return view('admin/admin_projects_edit', $data);
    }

    /**
     * Update project - POST request
     */
    public function update($id)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $id)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules (removed pro_code validation since it's auto-generated)
        $rules = [
            'title' => 'required|max_length[200]',
            'goal' => 'permit_empty|max_length[1000]',
            'description' => 'permit_empty',
            'initiation_date' => 'required|valid_date',
            'start_date' => 'permit_empty|valid_date',
            'end_date' => 'permit_empty|valid_date',
            'address_line' => 'permit_empty|max_length[255]',
            'country_id' => 'permit_empty|integer',
            'province_id' => 'permit_empty|integer',
            'district_id' => 'permit_empty|integer',
            'llg_id' => 'permit_empty|integer',
            'ward_name' => 'permit_empty|max_length[100]',
            'village_name' => 'permit_empty|max_length[100]',
            'gps_point' => 'permit_empty|max_length[100]',
            'status' => 'required|in_list[planning,active,on-hold,completed,cancelled]',
            'status_notes' => 'permit_empty',
            'baseline_year' => 'permit_empty|integer|greater_than[1900]|less_than_equal_to[2100]',
            'target_year' => 'permit_empty|integer|greater_than[1900]|less_than_equal_to[2100]',
            'gps_kml_file' => 'permit_empty|uploaded[gps_kml_file]|max_size[gps_kml_file,15360]|ext_in[gps_kml_file,kml,kmz]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for update (removed pro_code since it's auto-generated)
        // Ensure proper data types to prevent array-to-string conversion
        $updateData = [];

        // String fields - ensure they are strings, not arrays
        $stringFields = ['other_project_ids', 'title', 'goal', 'description', 'address_line', 'ward_name', 'village_name', 'gps_point', 'status', 'status_notes'];
        foreach ($stringFields as $field) {
            $value = $this->request->getPost($field);
            if (is_array($value)) {
                $updateData[$field] = ''; // Convert arrays to empty string
            } else {
                $updateData[$field] = (string)($value ?: '');
            }
        }

        // Date fields - can be null or valid date string
        $dateFields = ['initiation_date', 'start_date', 'end_date'];
        foreach ($dateFields as $field) {
            $value = $this->request->getPost($field);
            if (is_array($value) || empty($value)) {
                $updateData[$field] = null;
            } else {
                $updateData[$field] = (string)$value;
            }
        }

        // Integer fields - can be null or integer
        $intFields = ['country_id', 'province_id', 'district_id', 'llg_id', 'baseline_year', 'target_year'];
        foreach ($intFields as $field) {
            $value = $this->request->getPost($field);
            if (is_array($value) || empty($value)) {
                $updateData[$field] = null;
            } else {
                $updateData[$field] = (int)$value;
            }
        }

        // Add updated_by
        $updateData['updated_by'] = $adminUserId;

        // Handle GPS KML file upload
        $gpsKmlFile = $this->request->getFile('gps_kml_file');
        if ($gpsKmlFile && $gpsKmlFile->isValid() && !$gpsKmlFile->hasMoved()) {
            $newName = $gpsKmlFile->getRandomName();
            $uploadDir = ROOTPATH . 'public/uploads/projects/kml/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            if ($gpsKmlFile->move($uploadDir, $newName)) {
                $updateData['gps_kml_path'] = 'public/uploads/projects/kml/' . $newName;
            }
        } elseif (!empty($project['gps_kml_path'])) {
            // Keep existing file if no new file uploaded
            $updateData['gps_kml_path'] = $project['gps_kml_path'];
        }

        try {
            // Debug: Log the update data to see what's being passed
            log_message('debug', 'AdminProjectController: Update data: ' . print_r($updateData, true));

            // Temporarily disable model validation to avoid conflicts
            $this->projectModel->skipValidation(true);
            $updated = $this->projectModel->update($id, $updateData);
            $this->projectModel->skipValidation(false);

            if ($updated) {
                return redirect()->to(base_url('admin/projects'))
                               ->with('success', 'Project updated successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectModel->errors());
            }
        } catch (\Exception $e) {
            // Log the full exception for debugging
            log_message('error', 'AdminProjectController: Update error: ' . $e->getMessage() . ' | Trace: ' . $e->getTraceAsString());
            return redirect()->back()->withInput()->with('error', 'Error updating project: ' . $e->getMessage());
        }
    }

    /**
     * Delete project - POST request
     */
    public function destroy($id)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $id)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        try {
            // Set deleted_by before soft delete
            $this->projectModel->update($id, ['deleted_by' => $adminUserId]);
            $this->projectModel->delete($id);

            return redirect()->to(base_url('admin/projects'))
                           ->with('success', 'Project deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Error deleting project: ' . $e->getMessage());
        }
    }

    /**
     * Get projects with filters applied
     */
    private function getProjectsWithFilters($filters)
    {
        $builder = $this->projectModel->where('org_id', $filters['organization'])
                                     ->where('deleted_at', null);

        // Apply status filter
        if (!empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }

        // Apply search filter
        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('title', $filters['search'])
                   ->orLike('pro_code', $filters['search'])
                   ->orLike('description', $filters['search'])
                   ->groupEnd();
        }

        // Get projects with location data
        $projects = $builder->orderBy('created_at', 'DESC')->findAll();

        // Enrich each project with location names
        foreach ($projects as &$project) {
            $project = $this->enrichProjectData($project);
        }

        return $projects;
    }

    /**
     * Enrich project data with related information
     */
    private function enrichProjectData($project)
    {
        // Get country name
        if ($project['country_id']) {
            $country = $this->countryModel->find($project['country_id']);
            $project['country_name'] = $country ? $country['name'] : 'Unknown';
        } else {
            $project['country_name'] = null;
        }

        // Get province name
        if ($project['province_id']) {
            $province = $this->provinceModel->find($project['province_id']);
            $project['province_name'] = $province ? $province['name'] : 'Unknown';
        } else {
            $project['province_name'] = null;
        }

        // Get district name
        if ($project['district_id']) {
            $district = $this->districtModel->find($project['district_id']);
            $project['district_name'] = $district ? $district['name'] : 'Unknown';
        } else {
            $project['district_name'] = null;
        }

        // Get LLG name
        if ($project['llg_id']) {
            $llg = $this->llgModel->find($project['llg_id']);
            $project['llg_name'] = $llg ? $llg['name'] : 'Unknown';
        } else {
            $project['llg_name'] = null;
        }

        // Get creator name
        if ($project['created_by']) {
            $creator = $this->userModel->find($project['created_by']);
            $project['creator_name'] = $creator ? $creator['name'] : 'Unknown';
        } else {
            $project['creator_name'] = null;
        }

        return $project;
    }

    /**
     * Handle file uploads for projects (simplified for create method)
     */
    private function handleFileUploads($data, $existingProject = null)
    {
        // Handle GPS KML file upload
        $gpsKmlFile = $this->request->getFile('gps_kml_file');
        if ($gpsKmlFile && $gpsKmlFile->isValid() && !$gpsKmlFile->hasMoved()) {
            $newName = $gpsKmlFile->getRandomName();
            $uploadDir = ROOTPATH . 'public/uploads/projects/kml/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            if ($gpsKmlFile->move($uploadDir, $newName)) {
                $data['gps_kml_path'] = 'public/uploads/projects/kml/' . $newName;
            }
        }

        return $data;
    }

    /**
     * Generate project code in format: PR{NN}{YYYY}
     * Where NN is zero-padded sequential number for the year
     * Example: PR012025, PR022025, etc.
     * Note: Includes soft-deleted records to avoid code duplication
     */
    private function generateProjectCode($year)
    {
        // Get the highest project number for the given year
        // Include soft-deleted records to avoid duplicate codes
        $lastProject = $this->projectModel
            ->withDeleted() // Include soft-deleted records
            ->where('pro_code LIKE', 'PR%' . $year)
            ->orderBy('pro_code', 'DESC')
            ->first();

        $nextNumber = 1;

        if ($lastProject && $lastProject['pro_code']) {
            // Extract the number from the last project code
            // Format: PR{NN}{YYYY} - extract the NN part
            $codePattern = '/^PR(\d{2})' . $year . '$/';
            if (preg_match($codePattern, $lastProject['pro_code'], $matches)) {
                $lastNumber = intval($matches[1]);
                $nextNumber = $lastNumber + 1;
            }
        }

        // Ensure we don't exceed 99 projects per year (2-digit limit)
        if ($nextNumber > 99) {
            throw new \RuntimeException("Maximum number of projects (99) reached for year {$year}");
        }

        // Format: PR + zero-padded number (2 digits) + year
        return 'PR' . str_pad($nextNumber, 2, '0', STR_PAD_LEFT) . $year;
    }

























}
